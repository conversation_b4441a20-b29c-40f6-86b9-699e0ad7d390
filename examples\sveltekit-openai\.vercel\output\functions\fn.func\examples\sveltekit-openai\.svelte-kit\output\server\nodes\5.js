

export const index = 5;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/completion/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/5.CCgCD2k8.js","_app/immutable/chunks/CbnEJKeA.js","_app/immutable/chunks/DZAnoyUh.js","_app/immutable/chunks/rEQUNzlu.js","_app/immutable/chunks/BAWHIabC.js","_app/immutable/chunks/B84v40P9.js","_app/immutable/chunks/KJVUr3wG.js","_app/immutable/chunks/C6A9r9iV.js","_app/immutable/chunks/CPONKIYv.js","_app/immutable/chunks/Bhb75Ucg.js"];
export const stylesheets = [];
export const fonts = [];
