import { z as attr, q as copy_payload, t as assign_payload, m as pop, p as push, u as ensure_array_like, n as escape_html } from "../../../chunks/context.js";
import { B as <PERSON><PERSON>, A as Arrow_up } from "../../../chunks/button.js";
import { T as Textarea } from "../../../chunks/textarea.js";
import { n as notificationSchema } from "../../../chunks/schema.js";
import "clsx";
import { g as generateId, v as safeValidateTypes, x as asSchema, y as parsePartialJson, z as isDeepEqualData, B as isAbortError } from "../../../chunks/index2.js";
import { h as hasStructuredObjectContext, g as getStructuredObjectContext, K as KeyedStructuredObjectStore } from "../../../chunks/structured-object-context.svelte.js";
class StructuredObject {
  #options = {};
  #id = this.#options.id ?? generateId();
  #keyedStore;
  #store = this.#keyedStore.get(this.#id);
  #abortController;
  /**
   * The current value for the generated object. Updated as the API streams JSON chunks.
   */
  get object() {
    return this.#store.object;
  }
  set #object(value) {
    this.#store.object = value;
  }
  /** The error object of the API request */
  get error() {
    return this.#store.error;
  }
  /**
   * Flag that indicates whether an API request is in progress.
   */
  get loading() {
    return this.#store.loading;
  }
  constructor(options) {
    if (hasStructuredObjectContext()) {
      this.#keyedStore = getStructuredObjectContext();
    } else {
      this.#keyedStore = new KeyedStructuredObjectStore();
    }
    this.#options = options;
    this.#object = options.initialValue;
  }
  /**
   * Abort the current request immediately, keep the current partial object if any.
   */
  stop = () => {
    try {
      this.#abortController?.abort();
    } catch {
    } finally {
      this.#store.loading = false;
      this.#abortController = void 0;
    }
  };
  /**
   * Calls the API with the provided input as JSON body.
   */
  submit = async (input) => {
    try {
      this.#clearObject();
      this.#store.loading = true;
      const abortController = new AbortController();
      this.#abortController = abortController;
      const actualFetch = this.#options.fetch ?? fetch;
      const response = await actualFetch(this.#options.api, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...this.#options.headers
        },
        credentials: this.#options.credentials,
        signal: abortController.signal,
        body: JSON.stringify(input)
      });
      if (!response.ok) {
        throw new Error(await response.text() ?? "Failed to fetch the response.");
      }
      if (response.body == null) {
        throw new Error("The response body is empty.");
      }
      let accumulatedText = "";
      let latestObject = void 0;
      await response.body.pipeThrough(new TextDecoderStream()).pipeTo(new WritableStream({
        write: async (chunk) => {
          if (abortController?.signal.aborted) {
            throw new DOMException("Stream aborted", "AbortError");
          }
          accumulatedText += chunk;
          const { value } = await parsePartialJson(accumulatedText);
          const currentObject = value;
          if (!isDeepEqualData(latestObject, currentObject)) {
            latestObject = currentObject;
            this.#store.object = currentObject;
          }
        },
        close: async () => {
          this.#store.loading = false;
          this.#abortController = void 0;
          if (this.#options.onFinish != null) {
            const validationResult = await safeValidateTypes({
              value: latestObject,
              schema: asSchema(this.#options.schema)
            });
            this.#options.onFinish(validationResult.success ? {
              object: validationResult.value,
              error: void 0
            } : {
              object: void 0,
              error: validationResult.error
            });
          }
        }
      }));
    } catch (error) {
      if (isAbortError(error)) {
        return;
      }
      const coalescedError = error instanceof Error ? error : new Error(String(error));
      if (this.#options.onError) {
        this.#options.onError(coalescedError);
      }
      this.#store.loading = false;
      this.#store.error = coalescedError;
    }
  };
  /**
   * Clears the object state.
   */
  clear = () => {
    this.stop();
    this.#clearObject();
  };
  #clearObject = () => {
    this.#store.object = void 0;
    this.#store.error = void 0;
    this.#store.loading = false;
  };
}
function Trash($$payload, $$props) {
  let { size = 16 } = $$props;
  $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg"${attr("width", size)}${attr("height", size)} viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trash-icon lucide-trash"><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path><path d="M3 6h18"></path><path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg>`;
}
function _page($$payload, $$props) {
  push();
  const structuredObject = new StructuredObject({
    api: "/api/structured-object",
    schema: notificationSchema
  });
  let input = "";
  let userMessage = "";
  function handleSubmit(e) {
    userMessage = input;
    e.preventDefault();
    structuredObject.submit(input);
    input = "";
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    const each_array = ensure_array_like(structuredObject.object?.notifications ?? []);
    $$payload2.out += `<main class="flex flex-col items-center h-dvh w-dvw"><div class="grid h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px] p-2"><div class="w-full h-full overflow-y-auto">`;
    if (userMessage) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="my-2 max-w-[80%] justify-self-end rounded-md bg-secondary p-2 text-primary">Me: ${escape_html(userMessage)}</div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> <!--[-->`;
    for (let i = 0, $$length = each_array.length; i < $$length; i++) {
      let notification = each_array[i];
      $$payload2.out += `<div class="my-2 max-w-[80%] rounded-md bg-primary p-2 text-secondary">${escape_html(notification?.name)}: ${escape_html(notification?.message)}</div>`;
    }
    $$payload2.out += `<!--]--></div> <form class="relative">`;
    Textarea($$payload2, {
      placeholder: "Think of a theme to generate three notifications...",
      class: "h-full",
      onkeydown: (e) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          handleSubmit(e);
        }
      },
      get value() {
        return input;
      },
      set value($$value) {
        input = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> <div class="absolute bottom-3 right-3">`;
    Button($$payload2, {
      "aria-label": "Clear",
      type: "button",
      size: "icon",
      onclick: () => structuredObject.clear(),
      children: ($$payload3) => {
        Trash($$payload3, {});
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      "aria-label": "Send message",
      disabled: structuredObject.loading,
      type: "submit",
      size: "icon",
      children: ($$payload3) => {
        Arrow_up($$payload3, {});
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div></form></div></main>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
export {
  _page as default
};
