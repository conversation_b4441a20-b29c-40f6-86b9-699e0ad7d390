import { b as streamObject } from "../../../../chunks/index2.js";
import { n as notificationSchema } from "../../../../chunks/schema.js";
import { c as createOpenAI } from "../../../../chunks/index.js";
import { d as private_env } from "../../../../chunks/shared-server.js";
const openai = createOpenAI({
  apiKey: private_env?.OPENAI_API_KEY
});
async function POST({ request }) {
  const context = await request.json();
  const result = streamObject({
    model: openai("gpt-4o"),
    schema: notificationSchema,
    prompt: `Generate 3 notifications for a messages app in this context:` + context,
    onError: (error) => {
      console.error(error);
    }
  });
  return result.toTextStreamResponse();
}
export {
  POST
};
