var R=Object.defineProperty;var E=o=>{throw TypeError(o)};var j=(o,t,e)=>t in o?R(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e;var f=(o,t,e)=>j(o,typeof t!="symbol"?t+"":t,e),K=(o,t,e)=>t.has(o)||E("Cannot "+e);var i=(o,t,e)=>(K(o,t,"read from private field"),e?e.call(o):t.get(o)),l=(o,t,e)=>t.has(o)?E("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(o):t.set(o,e),g=(o,t,e,a)=>(K(o,t,"write to private field"),a?a.call(o,e):t.set(o,e),e);import{t as B,a as P,b as G}from"../chunks/CbnEJKeA.js";import"../chunks/rEQUNzlu.js";import{u as b,d as $,g as s,b as A,p as H,I as M,a as N,J as _,s as O,c as k,r as S,t as Q}from"../chunks/DZAnoyUh.js";import{s as U}from"../chunks/BAWHIabC.js";import{i as V}from"../chunks/B84v40P9.js";import{i as W}from"../chunks/KJVUr3wG.js";import{c as X,g as Y,T as Z}from"../chunks/C6A9r9iV.js";import{h as tt,g as et,K as it}from"../chunks/CPONKIYv.js";var r,x,c,y,p,m,u,d,v;class ot{constructor(t={}){l(this,r,{});l(this,x,b(()=>i(this,r).api??"/api/completion"));l(this,c,b(()=>i(this,r).id??Y()));l(this,y,b(()=>i(this,r).streamProtocol??"data"));l(this,p,$());l(this,m,b(()=>s(i(this,p)).get(s(i(this,c)))));l(this,u);l(this,d,$());f(this,"stop",()=>{var t;try{(t=i(this,u))==null||t.abort()}catch{}finally{s(i(this,m)).loading=!1,g(this,u,void 0)}});f(this,"complete",async(t,e)=>i(this,v).call(this,t,e));f(this,"handleSubmit",async t=>{var e;(e=t==null?void 0:t.preventDefault)==null||e.call(t),this.input&&await this.complete(this.input)});l(this,v,async(t,e)=>X({api:s(i(this,x)),prompt:t,credentials:i(this,r).credentials,headers:{...i(this,r).headers,...e==null?void 0:e.headers},body:{...i(this,r).body,...e==null?void 0:e.body},streamProtocol:s(i(this,y)),fetch:i(this,r).fetch,setCompletion:a=>{this.completion=a},setLoading:a=>{s(i(this,m)).loading=a},setError:a=>{s(i(this,m)).error=a},setAbortController:a=>{g(this,u,a??void 0)},onFinish:i(this,r).onFinish,onError:i(this,r).onError}));A(i(this,p),tt()?et():new it,!0),g(this,r,t),this.completion=t.initialCompletion??"",this.input=t.initialInput??""}get completion(){return s(i(this,m)).completions.get(s(i(this,c)))??""}set completion(t){s(i(this,m)).completions.set(s(i(this,c)),t)}get error(){return s(i(this,m)).error}get input(){return s(i(this,d))}set input(t){A(i(this,d),t,!0)}get loading(){return s(i(this,m)).loading}}r=new WeakMap,x=new WeakMap,c=new WeakMap,y=new WeakMap,p=new WeakMap,m=new WeakMap,u=new WeakMap,d=new WeakMap,v=new WeakMap;var st=B(`<main class="flex flex-col items-center h-dvh w-dvw"><div class="relative m-3 flex h-full w-full max-w-4xl grid-cols-1 grid-rows-[1fr,120px]"><div class="
        pointer-events-none absolute inset-0 -z-10 flex
        h-full min-h-[80px] w-full rounded-md border border-input bg-secondary px-3 py-2 text-base
        text-primary/50 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 md:text-sm
      "><!></div> <!></div></main>`);function ht(o,t){H(t,!1);const e=M(new ot),a=F(s(e).handleSubmit,300);function D(n){n.key==="Tab"&&s(e).completion&&(n.preventDefault(),_(e,s(e).input+=" "+s(e).completion),_(e,s(e).completion="")),a(n)}function F(n,h){let I;return function(...L){clearTimeout(I),I=setTimeout(()=>n(...L),h)}}W();var w=st(),T=k(w),C=k(T),q=k(C);{var z=n=>{var h=G();Q(()=>U(h,s(e).input+" "+s(e).completion)),P(n,h)};V(q,n=>{s(e).completion&&n(z)})}S(C);var J=O(C,2);Z(J,{placeholder:"Start typing to generate autocompletions...",class:"h-full bg-transparent",onkeydown:D,get value(){return s(e).input},set value(n){_(e,s(e).input=n)},$$legacy:!0}),S(T),S(w),P(o,w),N()}export{ht as component};
