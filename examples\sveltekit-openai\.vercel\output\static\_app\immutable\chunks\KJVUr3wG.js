import{h as d,m as g,e as i,o as l,j as m,q as v,g as p,v as b,w as h}from"./DZAnoyUh.js";function x(n=!1){const s=d,e=s.l.u;if(!e)return;let r=()=>b(s.s);if(n){let o=0,t={};const _=h(()=>{let c=!1;const a=s.s;for(const f in a)a[f]!==t[f]&&(t[f]=a[f],c=!0);return c&&o++,o});r=()=>p(_)}e.b.length&&g(()=>{u(s,r),l(e.b)}),i(()=>{const o=m(()=>e.m.map(v));return()=>{for(const t of o)typeof t=="function"&&t()}}),e.a.length&&i(()=>{u(s,r),l(e.a)})}function u(n,s){if(n.l.s)for(const e of n.l.s)p(e);s()}export{x as i};
