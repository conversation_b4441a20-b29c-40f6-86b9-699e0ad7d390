import{ab as Re,y as qe,ah as ke,T as X,C as D,ac as De,O as He,g as se,a3 as Be,P as Ge,R as Le,S as le,U as Z,D as P,ai as Pe,V as Ee,z as Te,W as Je,aj as $,ak as Ve,_ as Ke,ae as oe,al as ae,am as fe,an as ne,I as Ue,K as ue,F as We,ao as Ye,ap as Fe,aq as Qe,ar as Xe,B as Ze,as as $e,at as er,t as re,p as rr,f as tr,a as ar,c as ce,A as de,r as ve}from"./DZAnoyUh.js";import{n as nr,a as W,c as ir,t as je}from"./CbnEJKeA.js";import{h as pe,t as sr,j as lr,k as ye,l as he}from"./C6A9r9iV.js";import{p as B,i as or,b as be,r as fr}from"./B84v40P9.js";import{s as ge}from"./B9xEynmP.js";function kr(r,t){return t}function ur(r,t,e,f){for(var b=[],s=t.length,u=0;u<s;u++)Fe(t[u].e,b,!0);var A=s>0&&b.length===0&&e!==null;if(A){var m=e.parentNode;Qe(m),m.append(e),f.clear(),z(r,t[0].prev,t[s-1].next)}Xe(b,()=>{for(var T=0;T<s;T++){var c=t[T];A||(f.delete(c.k),z(r,c.prev,c.next)),Ze(c.e,!A)}})}function Er(r,t,e,f,b,s=null){var u=r,A={flags:t,items:new Map,first:null},m=(t&ke)!==0;if(m){var T=r;u=D?X(De(T)):T.appendChild(Re())}D&&He();var c=null,O=!1,n=Be(()=>{var l=e();return We(l)?l:l==null?[]:Ve(l)});qe(()=>{var l=se(n),a=l.length;if(O&&a===0)return;O=a===0;let k=!1;if(D){var y=Ge(u)===Le;y!==(a===0)&&(u=le(),X(u),Z(!1),k=!0)}if(D){for(var M=null,_,j=0;j<a;j++){if(P.nodeType===8&&P.data===Pe){u=P,k=!0,Z(!1);break}var v=l[j],w=f(v,j);_=Ce(P,A,M,null,v,w,j,b,t,e),A.items.set(w,_),M=_}a>0&&X(le())}D||cr(l,A,u,b,t,f,e),s!==null&&(a===0?c?Ee(c):c=Te(()=>s(u)):c!==null&&Je(c,()=>{c=null})),k&&Z(!0),se(n)}),D&&(u=P)}function cr(r,t,e,f,b,s,u){var o,h,p,g;var A=(b&Ye)!==0,m=(b&(ae|ne))!==0,T=r.length,c=t.items,O=t.first,n=O,l,a=null,k,y=[],M=[],_,j,v,w;if(A)for(w=0;w<T;w+=1)_=r[w],j=s(_,w),v=c.get(j),v!==void 0&&((o=v.a)==null||o.measure(),(k??(k=new Set)).add(v));for(w=0;w<T;w+=1){if(_=r[w],j=s(_,w),v=c.get(j),v===void 0){var Y=n?n.e.nodes_start:e;a=Ce(Y,t,a,a===null?t.first:a.next,_,j,w,f,b,u),c.set(j,a),y=[],M=[],n=a.next;continue}if(m&&dr(v,_,w,b),v.e.f&$&&(Ee(v.e),A&&((h=v.a)==null||h.unfix(),(k??(k=new Set)).delete(v))),v!==n){if(l!==void 0&&l.has(v)){if(y.length<M.length){var R=M[0],I;a=R.prev;var G=y[0],L=y[y.length-1];for(I=0;I<y.length;I+=1)me(y[I],R,e);for(I=0;I<M.length;I+=1)l.delete(M[I]);z(t,G.prev,L.next),z(t,a,G),z(t,L,R),n=R,a=L,w-=1,y=[],M=[]}else l.delete(v),me(v,n,e),z(t,v.prev,v.next),z(t,v,a===null?t.first:a.next),z(t,a,v),a=v;continue}for(y=[],M=[];n!==null&&n.k!==j;)n.e.f&$||(l??(l=new Set)).add(n),M.push(n),n=n.next;if(n===null)continue;v=n}y.push(v),a=v,n=v.next}if(n!==null||l!==void 0){for(var q=l===void 0?[]:Ve(l);n!==null;)n.e.f&$||q.push(n),n=n.next;var i=q.length;if(i>0){var d=b&ke&&T===0?e:null;if(A){for(w=0;w<i;w+=1)(p=q[w].a)==null||p.measure();for(w=0;w<i;w+=1)(g=q[w].a)==null||g.fix()}ur(t,q,d,c)}}A&&Ke(()=>{var x;if(k!==void 0)for(v of k)(x=v.a)==null||x.apply()}),oe.first=t.first&&t.first.e,oe.last=a&&a.e}function dr(r,t,e,f){f&ae&&fe(r.v,t),f&ne?fe(r.i,e):r.i=e}function Ce(r,t,e,f,b,s,u,A,m,T){var c=(m&ae)!==0,O=(m&$e)===0,n=c?O?Ue(b):ue(b):b,l=m&ne?ue(u):u,a={i:l,v:n,k:s,a:null,e:null,prev:e,next:f};try{return a.e=Te(()=>A(r,n,l,T),D),a.e.prev=e&&e.e,a.e.next=f&&f.e,e===null?t.first=a:(e.next=a,e.e.next=a.e),f!==null&&(f.prev=a,f.e.prev=a.e),a}finally{}}function me(r,t,e){for(var f=r.next?r.next.e.nodes_start:e,b=t?t.e.nodes_start:e,s=r.e.nodes_start;s!==f;){var u=er(s);b.before(s),s=u}}function z(r,t,e){t===null?r.first=e:(t.next=e,t.e.next=e&&e.e),e!==null&&(e.prev=t,e.e.prev=t&&t.e)}var vr=nr('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="currentColor" d="m11 7.825l-4.9 4.9q-.3.3-.7.288t-.7-.313q-.275-.3-.288-.7t.288-.7l6.6-6.6q.15-.15.325-.212T12 4.425t.375.063t.325.212l6.6 6.6q.275.275.275.688t-.275.712q-.3.3-.712.3t-.713-.3L13 7.825V19q0 .425-.288.713T12 20t-.712-.288T11 19z"></path></svg>');function Tr(r,t){let e=B(t,"size",3,16);var f=vr();re(()=>{pe(f,"width",e()),pe(f,"height",e())}),W(r,f)}var _e=r=>typeof r=="boolean"?`${r}`:r===0?"0":r,C=r=>!r||typeof r!="object"||Object.keys(r).length===0,pr=(r,t)=>JSON.stringify(r)===JSON.stringify(t);function Oe(r,t){r.forEach(function(e){Array.isArray(e)?Oe(e,t):t.push(e)})}function Me(r){let t=[];return Oe(r,t),t}var Ne=(...r)=>Me(r).filter(Boolean),Ie=(r,t)=>{let e={},f=Object.keys(r),b=Object.keys(t);for(let s of f)if(b.includes(s)){let u=r[s],A=t[s];Array.isArray(u)||Array.isArray(A)?e[s]=Ne(A,u):typeof u=="object"&&typeof A=="object"?e[s]=Ie(u,A):e[s]=A+" "+u}else e[s]=r[s];for(let s of b)f.includes(s)||(e[s]=t[s]);return e},Ae=r=>!r||typeof r!="string"?r:r.replace(/\s+/g," ").trim(),yr={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},Se=r=>r||void 0,K=(...r)=>Se(Me(r).filter(Boolean).join(" ")),ee=null,S={},te=!1,J=(...r)=>t=>t.twMerge?((!ee||te)&&(te=!1,ee=C(S)?sr:lr({...S,extend:{theme:S.theme,classGroups:S.classGroups,conflictingClassGroupModifiers:S.conflictingClassGroupModifiers,conflictingClassGroups:S.conflictingClassGroups,...S.extend}})),Se(ee(K(r)))):K(r),we=(r,t)=>{for(let e in t)r.hasOwnProperty(e)?r[e]=K(r[e],t[e]):r[e]=t[e];return r},hr=(r,t)=>{let{extend:e=null,slots:f={},variants:b={},compoundVariants:s=[],compoundSlots:u=[],defaultVariants:A={}}=r,m={...yr,...t},T=e!=null&&e.base?K(e.base,r==null?void 0:r.base):r==null?void 0:r.base,c=e!=null&&e.variants&&!C(e.variants)?Ie(b,e.variants):b,O=e!=null&&e.defaultVariants&&!C(e.defaultVariants)?{...e.defaultVariants,...A}:A;!C(m.twMergeConfig)&&!pr(m.twMergeConfig,S)&&(te=!0,S=m.twMergeConfig);let n=C(e==null?void 0:e.slots),l=C(f)?{}:{base:K(r==null?void 0:r.base,n&&(e==null?void 0:e.base)),...f},a=n?l:we({...e==null?void 0:e.slots},C(l)?{base:r==null?void 0:r.base}:l),k=C(e==null?void 0:e.compoundVariants)?s:Ne(e==null?void 0:e.compoundVariants,s),y=_=>{if(C(c)&&C(f)&&n)return J(T,_==null?void 0:_.class,_==null?void 0:_.className)(m);if(k&&!Array.isArray(k))throw new TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof k}`);if(u&&!Array.isArray(u))throw new TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof u}`);let j=(i,d,o=[],h)=>{let p=o;if(typeof d=="string")p=p.concat(Ae(d).split(" ").map(g=>`${i}:${g}`));else if(Array.isArray(d))p=p.concat(d.reduce((g,x)=>g.concat(`${i}:${x}`),[]));else if(typeof d=="object"&&typeof h=="string"){for(let g in d)if(d.hasOwnProperty(g)&&g===h){let x=d[g];if(x&&typeof x=="string"){let E=Ae(x);p[h]?p[h]=p[h].concat(E.split(" ").map(V=>`${i}:${V}`)):p[h]=E.split(" ").map(V=>`${i}:${V}`)}else Array.isArray(x)&&x.length>0&&(p[h]=x.reduce((E,V)=>E.concat(`${i}:${V}`),[]))}}return p},v=(i,d=c,o=null,h=null)=>{var p;let g=d[i];if(!g||C(g))return null;let x=(p=h==null?void 0:h[i])!=null?p:_==null?void 0:_[i];if(x===null)return null;let E=_e(x),V=Array.isArray(m.responsiveVariants)&&m.responsiveVariants.length>0||m.responsiveVariants===!0,H=O==null?void 0:O[i],N=[];if(typeof E=="object"&&V)for(let[Q,ie]of Object.entries(E)){let ze=g[ie];if(Q==="initial"){H=ie;continue}Array.isArray(m.responsiveVariants)&&!m.responsiveVariants.includes(Q)||(N=j(Q,ze,N,o))}let U=E!=null&&typeof E!="object"?E:_e(H),F=g[U||"false"];return typeof N=="object"&&typeof o=="string"&&N[o]?we(N,F):N.length>0?(N.push(F),o==="base"?N.join(" "):N):F},w=()=>c?Object.keys(c).map(i=>v(i,c)):null,Y=(i,d)=>{if(!c||typeof c!="object")return null;let o=new Array;for(let h in c){let p=v(h,c,i,d),g=i==="base"&&typeof p=="string"?p:p&&p[i];g&&(o[o.length]=g)}return o},R={};for(let i in _)_[i]!==void 0&&(R[i]=_[i]);let I=(i,d)=>{var o;let h=typeof(_==null?void 0:_[i])=="object"?{[i]:(o=_[i])==null?void 0:o.initial}:{};return{...O,...R,...h,...d}},G=(i=[],d)=>{let o=[];for(let{class:h,className:p,...g}of i){let x=!0;for(let[E,V]of Object.entries(g)){let H=I(E,d)[E];if(Array.isArray(V)){if(!V.includes(H)){x=!1;break}}else{let N=U=>U==null||U===!1;if(N(V)&&N(H))continue;if(H!==V){x=!1;break}}}x&&(h&&o.push(h),p&&o.push(p))}return o},L=i=>{let d=G(k,i);if(!Array.isArray(d))return d;let o={};for(let h of d)if(typeof h=="string"&&(o.base=J(o.base,h)(m)),typeof h=="object")for(let[p,g]of Object.entries(h))o[p]=J(o[p],g)(m);return o},q=i=>{if(u.length<1)return null;let d={};for(let{slots:o=[],class:h,className:p,...g}of u){if(!C(g)){let x=!0;for(let E of Object.keys(g)){let V=I(E,i)[E];if(V===void 0||(Array.isArray(g[E])?!g[E].includes(V):g[E]!==V)){x=!1;break}}if(!x)continue}for(let x of o)d[x]=d[x]||[],d[x].push([h,p])}return d};if(!C(f)||!n){let i={};if(typeof a=="object"&&!C(a))for(let d of Object.keys(a))i[d]=o=>{var h,p;return J(a[d],Y(d,o),((h=L(o))!=null?h:[])[d],((p=q(o))!=null?p:[])[d],o==null?void 0:o.class,o==null?void 0:o.className)(m)};return i}return J(T,w(),G(k),_==null?void 0:_.class,_==null?void 0:_.className)(m)},M=()=>{if(!(!c||typeof c!="object"))return Object.keys(c)};return y.variantKeys=M(),y.extend=e,y.base=T,y.slots=a,y.variants=c,y.defaultVariants=O,y.compoundSlots=u,y.compoundVariants=k,y};const xe=hr({base:"ring-offset-background focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border-input bg-background hover:bg-accent hover:text-accent-foreground border",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});var br=je("<a><!></a>"),gr=je("<button><!></button>");function Vr(r,t){rr(t,!0);let e=B(t,"variant",3,"default"),f=B(t,"size",3,"default"),b=B(t,"ref",15,null),s=B(t,"href",3,void 0),u=B(t,"type",3,"button"),A=fr(t,["$$slots","$$events","$$legacy","class","variant","size","ref","href","type","children"]);var m=ir(),T=tr(m);{var c=n=>{var l=br();let a;var k=ce(l);ge(k,()=>t.children??de),ve(l),be(l,y=>b(y),()=>b()),re(y=>a=he(l,a,{class:y,href:s(),...A}),[()=>ye(xe({variant:e(),size:f()}),t.class)]),W(n,l)},O=n=>{var l=gr();let a;var k=ce(l);ge(k,()=>t.children??de),ve(l),be(l,y=>b(y),()=>b()),re(y=>a=he(l,a,{class:y,type:u(),...A}),[()=>ye(xe({variant:e(),size:f()}),t.class)]),W(n,l)};or(T,n=>{s()?n(c):n(O,!1)})}W(r,m),ar()}export{Tr as A,Vr as B,Er as e,kr as i};
