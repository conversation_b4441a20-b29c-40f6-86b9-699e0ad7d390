var un=Array.isArray,on=Array.prototype.indexOf,Wn=Array.from,Xn=Object.defineProperty,V=Object.getOwnPropertyDescriptor,_n=Object.getOwnPropertyDescriptors,cn=Object.prototype,vn=Array.prototype,Ft=Object.getPrototypeOf,Nt=Object.isExtensible;const Qn=()=>{};function te(t){return t()}function Mt(t){for(var e=0;e<t.length;e++)t[e]()}const A=2,Lt=4,it=8,mt=16,N=32,B=64,nt=128,m=256,et=512,E=1024,O=2048,M=4096,H=8192,ut=16384,hn=32768,Yt=65536,ne=1<<17,pn=1<<19,qt=1<<20,dt=1<<21,G=Symbol("$state"),ee=Symbol("legacy props"),re=Symbol("");function Ht(t){return t===this.v}function dn(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function jt(t){return!dn(t,this.v)}function wn(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function En(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function yn(t){throw new Error("https://svelte.dev/e/effect_orphan")}function gn(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function ae(){throw new Error("https://svelte.dev/e/hydration_failed")}function le(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function mn(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function Tn(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function xn(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}let ot=!1;function se(){ot=!0}const fe=1,ie=2,ue=4,oe=8,_e=16,ce=1,ve=2,he=4,pe=8,de=16,we=1,Ee=2,An="[",Rn="[!",Dn="]",Tt={},y=Symbol(),ye="http://www.w3.org/1999/xhtml",ge="@attach";function bn(t){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}let h=null;function St(t){h=t}function me(t){return xt().get(t)}function Te(t,e){return xt().set(t,e),e}function xe(t){return xt().has(t)}function Ae(t,e=!1,n){var r=h={p:h,c:null,d:!1,e:null,m:!1,s:t,x:null,l:null};ot&&!e&&(h.l={s:null,u:null,r1:[],r2:Rt(!1)}),Cn(()=>{r.d=!0})}function Re(t){const e=h;if(e!==null){const u=e.e;if(u!==null){var n=p,r=v;e.e=null;try{for(var a=0;a<u.length;a++){var l=u[a];lt(l.effect),j(l.reaction),zt(l.fn)}}finally{lt(n),j(r)}}h=e.p,e.m=!0}return{}}function _t(){return!ot||h!==null&&h.l===null}function xt(t){return h===null&&bn(),h.c??(h.c=new Map(In(h)||void 0))}function In(t){let e=t.p;for(;e!==null;){const n=e.c;if(n!==null)return n;e=e.p}return null}function Y(t){if(typeof t!="object"||t===null||G in t)return t;const e=Ft(t);if(e!==cn&&e!==vn)return t;var n=new Map,r=un(t),a=S(0),l=v,u=i=>{var s=v;j(l);var f=i();return j(s),f};return r&&n.set("length",S(t.length)),new Proxy(t,{defineProperty(i,s,f){(!("value"in f)||f.configurable===!1||f.enumerable===!1||f.writable===!1)&&mn();var _=n.get(s);return _===void 0?(_=u(()=>S(f.value)),n.set(s,_)):I(_,u(()=>Y(f.value))),!0},deleteProperty(i,s){var f=n.get(s);if(f===void 0)s in i&&(n.set(s,u(()=>S(y))),pt(a));else{if(r&&typeof s=="string"){var _=n.get("length"),o=Number(s);Number.isInteger(o)&&o<_.v&&I(_,o)}I(f,y),pt(a)}return!0},get(i,s,f){var x;if(s===G)return t;var _=n.get(s),o=s in i;if(_===void 0&&(!o||(x=V(i,s))!=null&&x.writable)&&(_=u(()=>S(Y(o?i[s]:y))),n.set(s,_)),_!==void 0){var c=q(_);return c===y?void 0:c}return Reflect.get(i,s,f)},getOwnPropertyDescriptor(i,s){var f=Reflect.getOwnPropertyDescriptor(i,s);if(f&&"value"in f){var _=n.get(s);_&&(f.value=q(_))}else if(f===void 0){var o=n.get(s),c=o==null?void 0:o.v;if(o!==void 0&&c!==y)return{enumerable:!0,configurable:!0,value:c,writable:!0}}return f},has(i,s){var c;if(s===G)return!0;var f=n.get(s),_=f!==void 0&&f.v!==y||Reflect.has(i,s);if(f!==void 0||p!==null&&(!_||(c=V(i,s))!=null&&c.writable)){f===void 0&&(f=u(()=>S(_?Y(i[s]):y)),n.set(s,f));var o=q(f);if(o===y)return!1}return _},set(i,s,f,_){var Ot;var o=n.get(s),c=s in i;if(r&&s==="length")for(var x=f;x<o.v;x+=1){var X=n.get(x+"");X!==void 0?I(X,y):x in i&&(X=u(()=>S(y)),n.set(x+"",X))}o===void 0?(!c||(Ot=V(i,s))!=null&&Ot.writable)&&(o=u(()=>S(void 0)),I(o,u(()=>Y(f))),n.set(s,o)):(c=o.v!==y,I(o,u(()=>Y(f))));var Q=Reflect.getOwnPropertyDescriptor(i,s);if(Q!=null&&Q.set&&Q.set.call(_,f),!c){if(r&&typeof s=="string"){var It=n.get("length"),ht=Number(s);Number.isInteger(ht)&&ht>=It.v&&I(It,ht+1)}pt(a)}return!0},ownKeys(i){q(a);var s=Reflect.ownKeys(i).filter(o=>{var c=n.get(o);return c===void 0||c.v!==y});for(var[f,_]of n)_.v!==y&&!(f in i)&&s.push(f);return s},setPrototypeOf(){Tn()}})}function pt(t,e=1){I(t,t.v+e)}function At(t){var e=A|O,n=v!==null&&v.f&A?v:null;return p===null||n!==null&&n.f&m?e|=m:p.f|=qt,{ctx:h,deps:null,effects:null,equals:Ht,f:e,fn:t,reactions:null,rv:0,v:null,wv:0,parent:n??p}}function De(t){const e=At(t);return en(e),e}function be(t){const e=At(t);return e.equals=jt,e}function Bt(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)F(e[n])}}function On(t){for(var e=t.parent;e!==null;){if(!(e.f&A))return e;e=e.parent}return null}function Ut(t){var e,n=p;lt(On(t));try{Bt(t),e=sn(t)}finally{lt(n)}return e}function Vt(t){var e=Ut(t),n=(k||t.f&m)&&t.deps!==null?M:E;D(t,n),t.equals(e)||(t.v=e,t.wv=an())}const Z=new Map;function Rt(t,e){var n={f:0,v:t,reactions:null,equals:Ht,rv:0,wv:0};return n}function S(t,e){const n=Rt(t);return en(n),n}function Ie(t,e=!1){var r;const n=Rt(t);return e||(n.equals=jt),ot&&h!==null&&h.l!==null&&((r=h.l).s??(r.s=[])).push(n),n}function Oe(t,e){return I(t,zn(()=>q(t))),e}function I(t,e,n=!1){v!==null&&!b&&_t()&&v.f&(A|mt)&&!(w!=null&&w.includes(t))&&xn();let r=n?Y(e):e;return Nn(t,r)}function Nn(t,e){if(!t.equals(e)){var n=t.v;J?Z.set(t,e):Z.set(t,n),t.v=e,t.f&A&&(t.f&O&&Ut(t),D(t,t.f&m?M:E)),t.wv=an(),Gt(t,O),_t()&&p!==null&&p.f&E&&!(p.f&(N|B))&&(T===null?Bn([t]):T.push(t))}return e}function Gt(t,e){var n=t.reactions;if(n!==null)for(var r=_t(),a=n.length,l=0;l<a;l++){var u=n[l],i=u.f;i&O||!r&&u===p||(D(u,e),i&(E|m)&&(i&A?Gt(u,M):vt(u)))}}function Dt(t){console.warn("https://svelte.dev/e/hydration_mismatch")}let P=!1;function Ne(t){P=t}let R;function $(t){if(t===null)throw Dt(),Tt;return R=t}function Se(){return $(L(R))}function ke(t){if(P){if(L(R)!==null)throw Dt(),Tt;R=t}}function Ce(t=1){if(P){for(var e=t,n=R;e--;)n=L(n);R=n}}function Pe(){for(var t=0,e=R;;){if(e.nodeType===8){var n=e.data;if(n===Dn){if(t===0)return e;t-=1}else(n===An||n===Rn)&&(t+=1)}var r=L(e);e.remove(),e=r}}function Fe(t){if(!t||t.nodeType!==8)throw Dt(),Tt;return t.data}var kt,Sn,Kt,Zt;function Me(){if(kt===void 0){kt=window,Sn=/Firefox/.test(navigator.userAgent);var t=Element.prototype,e=Node.prototype,n=Text.prototype;Kt=V(e,"firstChild").get,Zt=V(e,"nextSibling").get,Nt(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),Nt(n)&&(n.__t=void 0)}}function wt(t=""){return document.createTextNode(t)}function Et(t){return Kt.call(t)}function L(t){return Zt.call(t)}function Le(t,e){if(!P)return Et(t);var n=Et(R);if(n===null)n=R.appendChild(wt());else if(e&&n.nodeType!==3){var r=wt();return n==null||n.before(r),$(r),r}return $(n),n}function Ye(t,e){if(!P){var n=Et(t);return n instanceof Comment&&n.data===""?L(n):n}return R}function qe(t,e=1,n=!1){let r=P?R:t;for(var a;e--;)a=r,r=L(r);if(!P)return r;var l=r==null?void 0:r.nodeType;if(n&&l!==3){var u=wt();return r===null?a==null||a.after(u):r.before(u),$(u),u}return $(r),r}function He(t){t.textContent=""}function $t(t){p===null&&v===null&&yn(),v!==null&&v.f&m&&p===null&&En(),J&&wn()}function kn(t,e){var n=e.last;n===null?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}function U(t,e,n,r=!0){var a=p,l={ctx:h,deps:null,nodes_start:null,nodes_end:null,f:t|O,first:null,fn:e,last:null,next:null,parent:a,prev:null,teardown:null,transitions:null,wv:0};if(n)try{bt(l),l.f|=hn}catch(s){throw F(l),s}else e!==null&&vt(l);var u=n&&l.deps===null&&l.first===null&&l.nodes_start===null&&l.teardown===null&&(l.f&(qt|nt))===0;if(!u&&r&&(a!==null&&kn(l,a),v!==null&&v.f&A)){var i=v;(i.effects??(i.effects=[])).push(l)}return l}function Cn(t){const e=U(it,null,!1);return D(e,E),e.teardown=t,e}function je(t){$t();var e=p!==null&&(p.f&N)!==0&&h!==null&&!h.m;if(e){var n=h;(n.e??(n.e=[])).push({fn:t,effect:p,reaction:v})}else{var r=zt(t);return r}}function Be(t){return $t(),Pn(t)}function Ue(t){const e=U(B,t,!0);return(n={})=>new Promise(r=>{n.outro?Yn(e,()=>{F(e),r(void 0)}):(F(e),r(void 0))})}function zt(t){return U(Lt,t,!1)}function Pn(t){return U(it,t,!0)}function Ve(t,e=[],n=At){const r=e.map(n);return Fn(()=>t(...r.map(q)))}function Fn(t,e=0){return U(it|mt|e,t,!0)}function Ge(t,e=!0){return U(it|N,t,!0,e)}function Jt(t){var e=t.teardown;if(e!==null){const n=J,r=v;Ct(!0),j(null);try{e.call(null)}finally{Ct(n),j(r)}}}function Wt(t,e=!1){var n=t.first;for(t.first=t.last=null;n!==null;){var r=n.next;n.f&B?n.parent=null:F(n,e),n=r}}function Mn(t){for(var e=t.first;e!==null;){var n=e.next;e.f&N||F(e),e=n}}function F(t,e=!0){var n=!1;(e||t.f&pn)&&t.nodes_start!==null&&(Ln(t.nodes_start,t.nodes_end),n=!0),Wt(t,e&&!n),ft(t,0),D(t,ut);var r=t.transitions;if(r!==null)for(const l of r)l.stop();Jt(t);var a=t.parent;a!==null&&a.first!==null&&Xt(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=null}function Ln(t,e){for(;t!==null;){var n=t===e?null:L(t);t.remove(),t=n}}function Xt(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function Yn(t,e){var n=[];Qt(t,n,!0),qn(n,()=>{F(t),e&&e()})}function qn(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var a of t)a.out(r)}else e()}function Qt(t,e,n){if(!(t.f&H)){if(t.f^=H,t.transitions!==null)for(const u of t.transitions)(u.is_global||n)&&e.push(u);for(var r=t.first;r!==null;){var a=r.next,l=(r.f&Yt)!==0||(r.f&N)!==0;Qt(r,e,l?n:!1),r=a}}}function Ke(t){tn(t,!0)}function tn(t,e){if(t.f&H){t.f^=H,t.f&E||(t.f^=E),W(t)&&(D(t,O),vt(t));for(var n=t.first;n!==null;){var r=n.next,a=(n.f&Yt)!==0||(n.f&N)!==0;tn(n,a?e:!1),n=r}if(t.transitions!==null)for(const l of t.transitions)(l.is_global||e)&&l.in()}}let z=[],yt=[];function nn(){var t=z;z=[],Mt(t)}function Hn(){var t=yt;yt=[],Mt(t)}function Ze(t){z.length===0&&queueMicrotask(nn),z.push(t)}function jn(){z.length>0&&nn(),yt.length>0&&Hn()}let tt=!1,rt=!1,at=null,C=!1,J=!1;function Ct(t){J=t}let K=[];let v=null,b=!1;function j(t){v=t}let p=null;function lt(t){p=t}let w=null;function en(t){v!==null&&v.f&dt&&(w===null?w=[t]:w.push(t))}let d=null,g=0,T=null;function Bn(t){T=t}let rn=1,st=0,k=!1;function an(){return++rn}function W(t){var o;var e=t.f;if(e&O)return!0;if(e&M){var n=t.deps,r=(e&m)!==0;if(n!==null){var a,l,u=(e&et)!==0,i=r&&p!==null&&!k,s=n.length;if(u||i){var f=t,_=f.parent;for(a=0;a<s;a++)l=n[a],(u||!((o=l==null?void 0:l.reactions)!=null&&o.includes(f)))&&(l.reactions??(l.reactions=[])).push(f);u&&(f.f^=et),i&&_!==null&&!(_.f&m)&&(f.f^=m)}for(a=0;a<s;a++)if(l=n[a],W(l)&&Vt(l),l.wv>t.wv)return!0}(!r||p!==null&&!k)&&D(t,E)}return!1}function Un(t,e){for(var n=e;n!==null;){if(n.f&nt)try{n.fn(t);return}catch{n.f^=nt}n=n.parent}throw tt=!1,t}function Pt(t){return(t.f&ut)===0&&(t.parent===null||(t.parent.f&nt)===0)}function ct(t,e,n,r){if(tt){if(n===null&&(tt=!1),Pt(e))throw t;return}if(n!==null&&(tt=!0),Un(t,e),Pt(e))throw t}function ln(t,e,n=!0){var r=t.reactions;if(r!==null)for(var a=0;a<r.length;a++){var l=r[a];w!=null&&w.includes(t)||(l.f&A?ln(l,e,!1):e===l&&(n?D(l,O):l.f&E&&D(l,M),vt(l)))}}function sn(t){var x;var e=d,n=g,r=T,a=v,l=k,u=w,i=h,s=b,f=t.f;d=null,g=0,T=null,k=(f&m)!==0&&(b||!C||v===null),v=f&(N|B)?null:t,w=null,St(t.ctx),b=!1,st++,t.f|=dt;try{var _=(0,t.fn)(),o=t.deps;if(d!==null){var c;if(ft(t,g),o!==null&&g>0)for(o.length=g+d.length,c=0;c<d.length;c++)o[g+c]=d[c];else t.deps=o=d;if(!k)for(c=g;c<o.length;c++)((x=o[c]).reactions??(x.reactions=[])).push(t)}else o!==null&&g<o.length&&(ft(t,g),o.length=g);if(_t()&&T!==null&&!b&&o!==null&&!(t.f&(A|M|O)))for(c=0;c<T.length;c++)ln(T[c],t);return a!==null&&a!==t&&(st++,T!==null&&(r===null?r=T:r.push(...T))),_}finally{d=e,g=n,T=r,v=a,k=l,w=u,St(i),b=s,t.f^=dt}}function Vn(t,e){let n=e.reactions;if(n!==null){var r=on.call(n,t);if(r!==-1){var a=n.length-1;a===0?n=e.reactions=null:(n[r]=n[a],n.pop())}}n===null&&e.f&A&&(d===null||!d.includes(e))&&(D(e,M),e.f&(m|et)||(e.f^=et),Bt(e),ft(e,0))}function ft(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)Vn(t,n[r])}function bt(t){var e=t.f;if(!(e&ut)){D(t,E);var n=p,r=h,a=C;p=t,C=!0;try{e&mt?Mn(t):Wt(t),Jt(t);var l=sn(t);t.teardown=typeof l=="function"?l:null,t.wv=rn;var u=t.deps,i}catch(s){ct(s,t,n,r||t.ctx)}finally{C=a,p=n}}}function Gn(){try{gn()}catch(t){if(at!==null)ct(t,at,null);else throw t}}function fn(){var t=C;try{var e=0;for(C=!0;K.length>0;){e++>1e3&&Gn();var n=K,r=n.length;K=[];for(var a=0;a<r;a++){var l=Zn(n[a]);Kn(l)}Z.clear()}}finally{rt=!1,C=t,at=null}}function Kn(t){var e=t.length;if(e!==0)for(var n=0;n<e;n++){var r=t[n];if(!(r.f&(ut|H)))try{W(r)&&(bt(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?Xt(r):r.fn=null))}catch(a){ct(a,r,null,r.ctx)}}}function vt(t){rt||(rt=!0,queueMicrotask(fn));for(var e=at=t;e.parent!==null;){e=e.parent;var n=e.f;if(n&(B|N)){if(!(n&E))return;e.f^=E}}K.push(e)}function Zn(t){for(var e=[],n=t;n!==null;){var r=n.f,a=(r&(N|B))!==0,l=a&&(r&E)!==0;if(!l&&!(r&H)){if(r&Lt)e.push(n);else if(a)n.f^=E;else try{W(n)&&bt(n)}catch(s){ct(s,n,null,n.ctx)}var u=n.first;if(u!==null){n=u;continue}}var i=n.parent;for(n=n.next;n===null&&i!==null;)n=i.next,i=i.parent}return e}function $n(t){for(var e;;){if(jn(),K.length===0)return e;rt=!0,fn()}}async function $e(){await Promise.resolve(),$n()}function q(t){var e=t.f,n=(e&A)!==0;if(v!==null&&!b){if(!(w!=null&&w.includes(t))){var r=v.deps;t.rv<st&&(t.rv=st,d===null&&r!==null&&r[g]===t?g++:d===null?d=[t]:(!k||!d.includes(t))&&d.push(t))}}else if(n&&t.deps===null&&t.effects===null){var a=t,l=a.parent;l!==null&&!(l.f&m)&&(a.f^=m)}return n&&(a=t,W(a)&&Vt(a)),J&&Z.has(t)?Z.get(t):t.v}function zn(t){var e=b;try{return b=!0,t()}finally{b=e}}const Jn=-7169;function D(t,e){t.f=t.f&Jn|e}function ze(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(G in t)gt(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&G in n&&gt(n)}}}function gt(t,e=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!e.has(t)){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{gt(t[r],e)}catch{}const n=Ft(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=_n(n);for(let a in r){const l=r[a].get;if(l)try{l.call(t)}catch{}}}}}export{G as $,Qn as A,F as B,P as C,R as D,Yt as E,un as F,Ft as G,cn as H,Ie as I,Oe as J,Rt as K,Te as L,me as M,xe as N,Se as O,Fe as P,An as Q,Rn as R,Pe as S,$ as T,Ne as U,Ke as V,Yn as W,y as X,zt as Y,Pn as Z,Ze as _,Re as a,V as a0,le as a1,ne as a2,be as a3,he as a4,jt as a5,pe as a6,ee as a7,ve as a8,ce as a9,Tt as aA,Dt as aB,ae as aC,Ue as aD,$n as aE,$e as aF,ge as aG,ye as aH,re as aI,_n as aJ,_t as aK,dn as aL,de as aa,wt as ab,Et as ac,Sn as ad,p as ae,we as af,Ee as ag,ue as ah,Dn as ai,H as aj,Wn as ak,fe as al,Nn as am,ie as an,oe as ao,Qt as ap,He as aq,qn as ar,_e as as,L as at,j as au,lt as av,v as aw,Cn as ax,Xn as ay,Me as az,I as b,Le as c,S as d,je as e,Ye as f,q as g,h,ot as i,zn as j,Y as k,bn as l,Be as m,Ce as n,Mt as o,Ae as p,te as q,ke as r,qe as s,Ve as t,De as u,ze as v,At as w,se as x,Fn as y,Ge as z};
