var i=e=>{throw TypeError(e)};var b=(e,t,s)=>t.has(e)||i("Cannot "+s);var r=(e,t,s)=>(b(e,t,"read from private field"),s?s.call(e):t.get(e)),a=(e,t,s)=>t.has(e)?i("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s);import{d as n,g as d,b as g}from"./DZAnoyUh.js";import{c as h,K as j}from"./Bhb75Ucg.js";var o,c,u;class x{constructor(){a(this,o,n());a(this,c,n(!1));a(this,u,n())}get object(){return d(r(this,o))}set object(t){g(r(this,o),t,!0)}get loading(){return d(r(this,c))}set loading(t){g(r(this,c),t,!0)}get error(){return d(r(this,u))}set error(t){g(r(this,u),t,!0)}}o=new WeakMap,c=new WeakMap,u=new WeakMap;class O extends j{constructor(t){super(x,t)}}const{hasContext:m,getContext:p,setContext:K}=h("StructuredObject");export{O as K,p as g,m as h,K as s};
