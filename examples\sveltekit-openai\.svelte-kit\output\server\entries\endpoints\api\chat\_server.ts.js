import { d as private_env } from "../../../../chunks/shared-server.js";
import { c as createOpenAI } from "../../../../chunks/index.js";
import { s as streamText, c as convertToModelMessages, a as stepCountIs } from "../../../../chunks/index2.js";
import { z } from "zod/v4";
const openai = createOpenAI({
  apiKey: private_env?.OPENAI_API_KEY
});
const POST = async ({ request }) => {
  const { messages } = await request.json();
  const result = streamText({
    model: openai("gpt-4o"),
    messages: convertToModelMessages(messages),
    stopWhen: stepCountIs(5),
    // multi-steps for server-side tools
    tools: {
      // server-side tool with execute function:
      getWeatherInformation: {
        description: "show the weather in a given city to the user",
        inputSchema: z.object({ city: z.string() }),
        execute: async ({ city: _ }) => {
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          const weatherOptions = ["sunny", "cloudy", "rainy", "snowy", "windy"];
          return weatherOptions[Math.floor(Math.random() * weatherOptions.length)];
        }
      },
      // client-side tool that starts user interaction:
      askForConfirmation: {
        description: "Ask the user for confirmation.",
        inputSchema: z.object({
          message: z.string().describe("The message to ask for confirmation.")
        })
      },
      // client-side tool that is automatically executed on the client:
      getLocation: {
        description: "Get the user location. Always ask for confirmation before using this tool.",
        inputSchema: z.object({})
      }
    },
    onError: (error) => {
      console.error(error);
    }
  });
  return result.toUIMessageStreamResponse();
};
export {
  POST
};
