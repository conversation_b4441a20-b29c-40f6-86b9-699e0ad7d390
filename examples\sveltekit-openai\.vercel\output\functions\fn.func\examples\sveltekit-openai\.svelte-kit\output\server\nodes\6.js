

export const index = 6;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/structured-object/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/6.BSIvcjdK.js","_app/immutable/chunks/CbnEJKeA.js","_app/immutable/chunks/DZAnoyUh.js","_app/immutable/chunks/BAWHIabC.js","_app/immutable/chunks/B84v40P9.js","_app/immutable/chunks/CIJPwbj8.js","_app/immutable/chunks/C6A9r9iV.js","_app/immutable/chunks/B9xEynmP.js","_app/immutable/chunks/BXRKiZpU.js","_app/immutable/chunks/Bhb75Ucg.js"];
export const stylesheets = [];
export const fonts = [];
