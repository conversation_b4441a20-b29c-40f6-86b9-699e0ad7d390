

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.CevGt4F0.js","_app/immutable/chunks/CbnEJKeA.js","_app/immutable/chunks/DZAnoyUh.js","_app/immutable/chunks/rEQUNzlu.js","_app/immutable/chunks/BAWHIabC.js","_app/immutable/chunks/KJVUr3wG.js","_app/immutable/chunks/M5zkiFCa.js","_app/immutable/chunks/C9BrO75Z.js","_app/immutable/chunks/ffQNQm1q.js"];
export const stylesheets = [];
export const fonts = [];
